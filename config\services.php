<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],
    
    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT_URI'),
    ],
     'google_calendar' => [
        'client_id' => env('GOOGLE_CALENDAR_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CALENDAR_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_CALENDAR_REDIRECT_URI'),
    ],

    'apple' => [
        'client_id' => env('APPLE_CLIENT_ID'),
        'client_secret' => env('APPLE_CLIENT_SECRET'),
        'redirect' => env('APPLE_REDIRECT_URI'),
        'key_id' => env('APPLE_KEY_ID'),
        'team_id' => env('APPLE_TEAM_ID'),
        'auth_key' => env('APPLE_AUTH_KEY'), // Path to .p8 file
    ],

    'trustpilot' => [
        'client_id' => env('TRUSTPILOT_CLIENT_ID'),
        'client_secret' => env('TRUSTPILOT_CLIENT_SECRET'),
        'redirect_uri' => env('TRUSTPILOT_REDIRECT_URI'),
        'authorize_url' => 'https://authenticate.trustpilot.com/',
        'token_url' => 'https://api.trustpilot.com/v1/oauth/oauth-business-users-for-applications/accesstoken',
        'business_units_url' => 'https://api.trustpilot.com/v1/private/business-units/find',
    ],
    'stripe' => [
        'publishable_key' => 'pk_test_51RQmk3GsvAZUDnSw9by8pjOr6XpS8DKxVqgtcWMZZAboc5lqwEJOAQcmLJxpUel8BatZGfOCPK2GjFbjn5jAQHnn00u7Bs8Ea3',
        'secret_key' => 'sk_test_51RQmk3GsvAZUDnSw8SDBD9A0VnZcoie9L8glcKUzOazETvp9JxpGCMGvrsAmj5hqkrxakftX7odn5xnGlP0VazNk00AYEVXnVQ',
        'webhook_secret' => 'whsec_E30OPkg9vEu3KdT3Z02fId9TGckU8B06',
        'connect_webhook_secret' => 'whsec_i3PQoB0qRnWBQzpsAvbO9DezZHL0YaZv',
        'disconnect_webhook_secret' => 'whsec_hkRjhBSoqz9P4c2QLMpiR9vEkqoZJAqJ',
        'return_url' => 'https://anders.democustomprojects.com/setting?tab=stripe',
        'refresh_url' => 'https://anders.democustomprojects.com/setting?tab=stripe',
    ],
    'pusher' => [
        'app_id' => env('PUSHER_APP_ID'),
        'app_key' => env('PUSHER_APP_KEY'),
        'app_secret' => env('PUSHER_APP_SECRET'),
        'app_cluster' => env('PUSHER_APP_CLUSTER'),
    ],
];
