<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Staff;
use App\Models\StaffAvailability;
use App\Models\StaffVacation;
use App\Models\SubCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class StaffController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $staffs = auth()->user()->staffs()->with('category')->paginate(10);
        $categories = Category::all();
        $subcategories = SubCategory::all();
        return view('dashboard.business.staffs.index', compact('staffs', 'categories', 'subcategories'));
    }

    /**
     * Filter staff members based on search and status
     */
    public function filterStaffs(Request $request)
    {
        $search = $request->filled('search') ? trim($request->get('search')) : '';
        $status = $request->filled('status') && $request->get('status') !== 'all' ? $request->get('status') : '';
        $limit = 10;

        // Base query
        $query = Staff::with('category');

        // Apply search filter if search term is provided
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($status !== '') {
            if ($status === 'active') {
                $query->where('status', 1);
            } elseif ($status === 'inactive') {
                $query->where('status', 0);
            }
        }

        // Get paginated results
        $staffs = $query->paginate($limit);

        // Generate table HTML
        $html = view('dashboard.business.staffs.partials.staff-table', ['staffs' => $staffs])->render();

        // Generate pagination HTML
        $paginationHtml = '';
        if ($staffs->hasPages()) {
            $paginationHtml = $staffs->appends(request()->query())->links('pagination::bootstrap-4')->render();
        }

        return response()->json([
            'html' => $html,
            'pagination' => $paginationHtml,
            'count' => $staffs->count(),
            'total' => $staffs->total(),
            'has_pages' => $staffs->hasPages()
        ]);
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::all();
        return view('dashboard.business.staffs.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'avatar' => ['required', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'email' => ['required', 'regex:' . config('constant.email_regex')],
            'category_id' => 'required',
            'subcategory_id' => 'required',
            'availability_data' => 'required',
            'phone' => ['required', 'regex:' . config('constant.phone_regex')],
            // 'facebook' => ['nullable', 'regex:' . config('constant.url_regex')],
            // 'instagram' => ['nullable', 'regex:' . config('constant.url_regex')],
            // 'tiktok' => ['nullable', 'regex:' . config('constant.url_regex')],
            // 'youtube' => ['nullable', 'regex:' . config('constant.url_regex')],
            'availability_data' => 'nullable|json',

            'vacations_data' => 'nullable|json',
            'recurring_data' => 'nullable|json',
        ], [
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'name.required' => 'Please enter name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['input']['max'],
            'email.required' => 'Please enter email',
            'email.regex' => 'Please enter valid email',
            'category_id.required' => 'Please select category',
            'phone.required' => 'Please enter phone number',
            'phone.regex' => 'Please enter valid phone number',
            'subcategory_id.required' => 'Please select sub category',
            'availability_data.required' => 'Please select availability',
            // 'facebook.regex' => 'Please enter valid facebook url',
            // 'instagram.regex' => 'Please enter valid instagram url',
            // 'tiktok.regex' => 'Please enter valid tiktok url',
            // 'youtube.regex' => 'Please enter valid youtube url',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $staffData = $validator->validated();
            $staffData['user_id'] = auth()->user()->id;
            if ($request->hasFile('avatar')) {
                $staffData['image'] = $this->storeImage('staff-images', $request->file('avatar'));
            }
            $staff = Staff::create($staffData);
            // Process recurring data if provided
            if ($request->filled('recurring_data')) {
                $recurringData = json_decode($request->recurring_data, true);
                $staff->update([
                    'is_recurring' => $recurringData['recurring'] ?? false,
                    'recurring_duration' => $recurringData['duration'] ?? null,
                    'custom_weeks' => $recurringData['customWeeks'] ?? null,
                ]);
            }
            // Process availability data if provided
            if ($request->filled('availability_data')) {
                $this->processStaffAvailability($staff->id, $request);
            }
            DB::commit();
            return redirect()->route('staffs.index')->with(["type" => "success", "title" => "Created", "message" => 'Staff Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->route('staffs.index')->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();
        return view('dashboard.business.staffs.show', compact('staff'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();
        $availabilities = StaffAvailability::where('staff_id', $staff->id)->get();
        $vacations = StaffVacation::where('staff_id', $staff->id)
            ->pluck('vacation_date')
            ->map(function ($date) {
                // Convert to simple date format (YYYY-MM-DD) for frontend
                return Carbon::parse($date)->format('Y-m-d');
            })
            ->toArray();

        // Group by day AND date to keep each specific date's slots separate
        $availabilityByDayAndDate = [];
        foreach ($availabilities as $availability) {
            $key = $availability->day . '_' . $availability->date;

            if (!isset($availabilityByDayAndDate[$key])) {
                $availabilityByDayAndDate[$key] = [
                    'day' => $availability->day,
                    'date' => $availability->date,
                    'slots' => []
                ];
            }

            $availabilityByDayAndDate[$key]['slots'][] = [
                'start_time' => $availability->start_time,
                'end_time' => $availability->end_time
            ];
        }

        // Debug: Log the raw data grouped by day and date
        Log::info('Availability grouped by day and date:', $availabilityByDayAndDate);

        // Now group by day only for the edit modal, taking the first occurrence of each day
        $dayGroups = [];
        foreach ($availabilityByDayAndDate as $dayDateData) {
            $day = $dayDateData['day'];

            // Only take the first occurrence of each day for edit modal
            if (!isset($dayGroups[$day])) {
                $dayGroups[$day] = $dayDateData;
            }
        }

        // Format for edit modal: first slot = main_slot, rest = additional_slots
        $formattedAvailability = [];
        foreach ($dayGroups as $dayData) {
            $slots = $dayData['slots'];
            $formattedAvailability[] = [
                'day' => $dayData['day'],
                'main_slot' => $slots[0] ?? null,
                'additional_slots' => array_slice($slots, 1)
            ];
        }
        $staff->availability_data = $formattedAvailability;
        $staff->vacations_data = $vacations;
        $staff->recurring_data = [
            'recurring' => $staff->is_recurring,
            'duration' => $staff->recurring_duration,
            'customWeeks' => $staff->custom_weeks
        ];
        return response()->json($staff);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'avatar' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'email' => ['required', 'regex:' . config('constant.email_regex')],
            'category_id' => 'required',
            'subcategory_id' => 'required',
            'phone' => ['required', 'regex:' . config('constant.phone_regex')],
            // 'facebook' => ['nullable', 'regex:' . config('constant.url_regex')],
            // 'instagram' => ['nullable', 'regex:' . config('constant.url_regex')],
            // 'tiktok' => ['nullable', 'regex:' . config('constant.url_regex')],
            // 'youtube' => ['nullable', 'regex:' . config('constant.url_regex')],
            'availability_data' => 'nullable|json',
            'vacations_data' => 'nullable|json',
            'recurring_data' => 'nullable|json',
        ], [
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'name.required' => 'Please enter name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['input']['max'],
            'email.required' => 'Please enter email',
            'email.regex' => 'Please enter valid email',
            'category_id.required' => 'Please select category',
            'subcategory_id.required' => 'Please select sub category',
            'phone.required' => 'Please enter phone number',
            'phone.regex' => 'Please enter valid phone number',
            // 'facebook.regex' => 'Please enter valid facebook url',
            // 'instagram.regex' => 'Please enter valid instagram url',
            // 'tiktok.regex' => 'Please enter valid tiktok url',
            // 'youtube.regex' => 'Please enter valid youtube url',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $staff = Staff::where('ids', $id)->firstOrFail();
            $staffData = $validator->validated();
            if ($request->hasFile('avatar')) {
                $this->deleteImage($staff->image);
                $staffData['image'] = $this->storeImage('staff-images', $request->file('avatar'));
            }
            $staff->update($staffData);
            // Process recurring data if provided
            if ($request->filled('recurring_data')) {
                $recurringData = json_decode($request->recurring_data, true);
                $staff->update([
                    'is_recurring' => $recurringData['recurring'] ?? false,
                    'recurring_duration' => $recurringData['duration'] ?? null,
                    'custom_weeks' => $recurringData['customWeeks'] ?? null,
                ]);
            }
            // Process availability data if provided
            if ($request->filled('availability_data')) {
                $this->processStaffAvailability($staff->id, $request);
            }
            DB::commit();
            return response()->json(["type" => "success", "title" => "Updated", "message" => 'Staff Updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();
        $this->deleteImage($staff->image);
        $staff->delete();
        return response()->json(["type" => "success", "title" => "Deleted", "message" => 'Staff Deleted Successfully!!']);
    }

    public function updateStatus(Request $request)
    {
        $staff = Staff::findOrFail($request->staff_id);
        $staff->status = $request->status;
        $staff->save();
        return response()->json(['success' => true]);
    }

    /**
     * Get day of week number (0 = Monday, 6 = Sunday)
     */
    private function getDayOfWeekNumber($dayName)
    {
        $days = [
            'Monday' => 0,
            'Tuesday' => 1,
            'Wednesday' => 2,
            'Thursday' => 3,
            'Friday' => 4,
            'Saturday' => 5,
            'Sunday' => 6
        ];

        return $days[$dayName] ?? 0;
    }

    /**
     * Process staff availability data from the form
     */
    private function processStaffAvailability($staffId, Request $request)
    {
        $availabilityData = json_decode($request->availability_data, true);
        $vacationsData = json_decode($request->vacations_data, true);
        $recurringData = json_decode($request->recurring_data, true);
        // Delete existing vacation records for this staff
        StaffVacation::where('staff_id', $staffId)->delete();
        // Save vacation days
        if (!empty($vacationsData)) {
            foreach ($vacationsData as $vacationDate) {
                StaffVacation::create([
                    'staff_id' => $staffId,
                    'vacation_date' => $vacationDate
                ]);
            }
        }
        // Calculate the number of weeks to generate
        $weeksToGenerate = 1; // Default to 1 week
        if ($recurringData && $recurringData['recurring']) {
            $weeksToGenerate = $this->calculateWeeksFromRecurring($recurringData);
        }
        // Generate availability records
        if (!empty($availabilityData) && is_array($availabilityData)) {
            $this->generateStaffAvailabilityRecords($staffId, $availabilityData, $weeksToGenerate);
        }
    }
    /**
     * Calculate weeks from recurring data
     */
    private function calculateWeeksFromRecurring($recurringData)
    {
        switch ($recurringData['duration']) {
            case '4':
                return 4;
            case '8':
                return 8;
            case 'custom':
                return (int) $recurringData['customWeeks'];
            default:
                return 1;
        }
    }

    /**
     * Generate staff availability records
     */
    private function generateStaffAvailabilityRecords($staffId, $availabilityData, $weeksToGenerate)
    {
        $startDate = Carbon::now()->startOfWeek(); // Start from current week's Monday
        StaffAvailability::where('staff_id', $staffId)->delete();

        for ($week = 0; $week < $weeksToGenerate; $week++) {
            foreach ($availabilityData as $dayData) {
                // Skip if required fields are missing
                if (!isset($dayData['day']) || !isset($dayData['start_time']) || !isset($dayData['end_time'])) {
                    continue;
                }

                $dayOfWeek = $this->getDayOfWeekNumber($dayData['day']);
                $currentDate = $startDate->copy()->addWeeks($week)->addDays($dayOfWeek);
                StaffAvailability::create([
                    'staff_id' => $staffId,
                    'date' => $currentDate->format('Y-m-d'),
                    'day' => $dayData['day'],
                    'start_time' => $dayData['start_time'],
                    'end_time' => $dayData['end_time']
                ]);
                if (!empty($dayData['additional_slots']) && is_array($dayData['additional_slots'])) {
                    foreach ($dayData['additional_slots'] as $slot) {
                        // Skip if additional slot is missing required fields
                        if (!isset($slot['start_time']) || !isset($slot['end_time'])) {
                            continue;
                        }

                        StaffAvailability::create([
                            'staff_id' => $staffId,
                            'date' => $currentDate->format('Y-m-d'),
                            'day' => $dayData['day'],
                            'start_time' => $slot['start_time'],
                            'end_time' => $slot['end_time']
                        ]);
                    }
                }
            }
        }
    }
}
