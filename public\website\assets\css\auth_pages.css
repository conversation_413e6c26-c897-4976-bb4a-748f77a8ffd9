
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&family=Sora:wght@100..800&display=swap');

.blue-text{color: #020C87;}
.gray-text{color: gray;}

h3{font-size: 34px; font-weight: 600;}
h4{font-size: 20px; font-weight: 600;}
.fs-14{font-size: 14px;}
.fs-16{font-size: 16px;}
.fw-600{font-weight: 600;}

.max-100 { max-height: 100vh; overflow: hidden; }
a{text-decoration: none;}

.professional-acc-form #acc-form, .register-form { position: relative; margin-top: 20px }
.professional-acc-form #acc-form fieldset { padding-bottom: 20px; position: relative }
.professional-acc-form .form-card { text-align: left }
.professional-acc-form #acc-form fieldset:not(:first-of-type), .register-form fieldset:not(:first-of-type) { display: none }

 .customer-registration #acc-form input[type="password"],    .professional-acc-form #acc-form input[type="text"], #acc-form input[type="email"], #acc-form input[type=tel], #acc-form input[type="number"], #acc-form input[type="date"], #acc-form input[type="url"], #acc-form textarea { padding: 14px 12px 14px 16px; border-radius: 10px; border: 1px solid #DCDDE8; background: #FFF; margin-top: 2px; width: 100%; font-size: 14px; outline: unset; }
.professional-acc-form input[type="text"], #acc-form input[type="email"], #acc-form input[type=tel], #acc-form input[type="number"], #acc-form input[type="date"], #acc-form input[type="url"], #acc-form textarea {margin-bottom: 2em;}

#acc-form input[type=tel]  {padding: 14px 21px 15px 50px;}
.iti--allow-dropdown{ width: 100%; margin-bottom: 30px; }

.professional-acc-form #progressbar li {border-bottom: 8px solid; border-radius: 10px;}
.professional-acc-form .card { z-index: 0; border: none; position: relative; }
.professional-acc-form #progressbar { margin-bottom: 30px; overflow: hidden; color: lightgrey; gap: 10px; display: flex; }
.professional-acc-form #progressbar .active { color: #020C87; }
.professional-acc-form #progressbar li { list-style-type: none; font-size: 15px; width: 20%; float: left; position: relative; font-weight: 400; }
.professional-acc-form .fit-image { width: 100%; object-fit: cover; }
.professional-acc-form label{ font-size: 14px; font-weight: 500; color: #000;}

.professional-acc-form h2{color: #000; font-family: Sora; font-size: 34px; font-style: normal; font-weight: 600;}
.professional-acc-form    p{font-size: 14px; font-style: normal; font-weight: 400;}
.professional-acc-form ol, ul { padding-left: 0; }
.professional-acc-form .next, .professional-acc-form .submit {padding: 10px 42px; border-radius: 10px; background: #020C87; color: #FFF; font-size: 16px;}

.professional-acc-form i.previous.action-button-previous, .first-stepper-form i.previous.action-button-previous { cursor: pointer; border-radius: 50%; background: #F1F1F1; height: 45px; display: flex; width: 45px; text-align: center; justify-content: center; align-items: center; }
.professional-acc-form .google_btn, .outlook_btn{border-radius: 10px;     color: black; font-size: 14px;  font-weight: 600; border: 1px solid rgba(220, 221, 232, 0.87); padding: 10px 30px; background: #F9F9F9;}
.professional-acc-form .next, .first-stepper-form .next{cursor: pointer;}
.professional-acc-form .google_btn, .outlook_btn{border-radius: 10px; border: 1px solid rgba(220, 221, 232, 0.87); padding: 10px 30px; background: #F9F9F9;}
.professional-acc-form .next, .first-stepper-form .next{cursor: pointer;}

.blue-border-btn{padding: 10px 42px; border: 2px solid #020C87; border-radius: 10px; background: var(--white-smoke); color: #020C87; font-size: 16px;}

.error-message {display: none;  color: red;  font-size: 12px; margin-top: 5px;}
.checkbox-box.error {border-color: red !important;  background-color: #ffe6e6;}
.services-section .custom-checkbox input[type="checkbox"]:checked + .checkbox-box::after { content: "";  position: absolute;   top: 10px; right: 10px;  background-image: url('../images/Checkbox-form.svg'); background-repeat: no-repeat;  background-size: contain; height: 20px; width: 20px;}

/* Custom Radio btns - Step 2 */
.services-section .custom-radio { position: relative; display: inline-block; width: 100%; }
.services-section .custom-radio input[type="radio"] { display: none; }
.services-section .radio-box {display: flex; flex-direction: column; align-items: flex-start; justify-content: center; position: relative; transition: border-color 0.3s, box-shadow 0.3s; }
.services-section .custom-radio input[type="radio"]:checked + .radio-box { border-color: #0033cc; }
.services-section .custom-radio input[type="radio"]:checked + .radio-box::after {content: ""; position: absolute; top: 10px; right: 10px; background-image: url(./Checkbox-form.svg); background-repeat: no-repeat; height: 20px; width: 20px; }
.services-section .label-text {margin-top: 10px;  font-weight: 500;}

/* Checkbox - step 2 */
.custom-checkbox-group { display: flex; flex-wrap: wrap; gap: 12px; background-color: #f9f9f9; padding: 16px; border-radius: 12px; }
.custom-checkbox { position: relative; }
.custom-checkbox input[type="checkbox"] { display: none; }
.checkbox-label { display: inline-flex; align-items: center; gap: 8px; border: 2px solid #ccc; border-radius: 20px; padding: 8px 16px; font-size: 14px; background-color: white; cursor: pointer; transition: all 0.3s; }
.checkbox-label::before { content: ""; width: 18px; height: 18px; border: 2px solid #ccc; border-radius: 5px; display: inline-block; transition: all 0.3s; background-color: white; }
.custom-checkbox input[type="checkbox"]:checked + .checkbox-label { border-color: #1c2c8f; color: #1c2c8f; font-weight: 500; }
input[type="checkbox"]:checked + .checkbox-label::before { background-image: url("../images/Checkbox-form.svg"); background-repeat: no-repeat; background-position: center; border: unset; }
.services-section .custom-radio input[type="radio"]:checked + .custom-radio { border-color: #1c2c8f; color: #1c2c8f; font-weight: 500; }

/* second checkboxes */
.services-section.custom-radio-group2{display: flex; flex-wrap: wrap; width: 100%; gap: 17px;}
.services-section .custom-radio {position: relative; display: inline-block;}
.services {height: 605px; overflow-y: auto; padding-right: 20px;}
.services::-webkit-scrollbar {
  width: 50px !important; /* adjust as needed */
}

.gray-card{border-radius: 10px;  border: 1px solid #DCDDE8; background: #FFF; padding: 25px;}
.time-picker-calendar label.days{  display: flex;  height: 44px; gap: 10px; align-items: center;}

.custom-radio input[type="checkbox"] { display: none; }
.services-section .custom-radio  input[type="checkbox"]:checked + .checkbox-label::before { background-image: url("../images/Checkbox-form.svg"); background-repeat: no-repeat; background-position: center; border: unset; }

/*Image Input holder*/
.image-input-empty  {background-image: url('../images/image_input_holder.png');}
[data-bs-theme="dark"] .image-input-placeholder {background-image: url('../images/image_input_holder.png');}
.image_label { display: flex; justify-content: space-between; width: 51%; align-items: center; margin-bottom: 3em; gap: 40px; }
/* .create_profile .image-input [data-kt-image-input-action=cancel], .image-input [data-kt-image-input-action=remove] { position: absolute;  z-index: 1; left: 19em; top: 12em; border: 1px solid green; background: #F0F9F4; color: black; width: 123px; padding: 7px; display: flex; justify-content: center; } */
.image-input.image-input-outline .image-input-wrapper {  border-radius: 50%; margin: -3px;}
.image-input [data-kt-image-input-action=change] { left: 19em; top: 4em; width: 243px; z-index: 1; }
.image-input-wrapper { background-image: url('../images/image_input_holder.png'); background-size: cover; background-position: center; background-repeat: no-repeat; border-radius: 50%; width: 125px; height: 125px; }

#toggle-password i, #toggle-password-otp i , #toggle-password-confirm i { cursor: pointer;}
button.delete-block { border: 1px solid #FFF; background: #b42e2ef2; color: #FFF; border-radius: 20px; padding: 10px 15px; }
.first-stepper-form .step-1 img.next.action-button {   width: 25px;  height: 25px;}
button.delete-block2 { border: 1px solid #FFF; background: #b42e2ef2; color: #FFF; border-radius: 5px; padding: 5px 15px; }

/* .iti__flag-container { margin-left: 18px; } */

/*Dropzone*/
.upload-box { padding: 30px; background: var(--white-smoke); cursor: pointer; border-radius: 4px; border: 1px dashed gray; background: #FFF; display: flex; justify-content: center; align-items: center; flex-direction: column; gap: 10px;}
.upload-box { border: 2px dashed #ccc; border-radius: 10px; padding: 25px; text-align: center; cursor: pointer; background-color: #f9f9f9; transition: background 0.3s ease; position: relative; }
.upload-box:hover { background-color: #eee; }
.upload-box img { width: 60px; height: 40px; }

/* Preview container for certificates (images) */
.add-file { display: block; font-size: 18px; margin-top: 10px; color: #555; }
.preview-container { display: flex; flex-wrap: wrap; margin-top: 15px; gap: 15px; height: 10px; }
.preview-box { width: 160px; height: 95px; border: 1px solid #ddd; border-radius: 10px; overflow: hidden; position: relative; background-color: #fff; box-shadow: 0 0 5px rgba(0, 0, 0, 0.1); top: -22.3em; left: 39%; }
.preview-box img { width: 100%; height: 100%; object-fit: cover; }

/* Preview for file uploads (PDFs, ZIPs, DOCs) */
.preview-box-file { display: flex; align-items: center; gap: 12px; border: 1px solid #ddd; padding: 12px; border-radius: 10px; background-color: #fff; box-shadow: 0 0 5px rgba(0,0,0,0.05); position: relative; width: 100%; max-width: 300px; }

/* Remove button for both */
.remove-image, .remove-file { position: absolute; top: 5px; right: 8px; background: red; color: white; border: none; border-radius: 50%; width: 22px; height: 22px; font-size: 14px; line-height: 20px; cursor: pointer; text-align: center; padding: 0; display: flex; align-items: center; justify-content: center; }
.upload-cert-btn{ padding: 7px 20px; border-radius: 20px; background: #020C87; color: #FFF;}
.cert-excep { display: flex; width: 200px; align-items: center; gap: 8px; margin-bottom: 2em; }
.addMoreBtn { border-radius: 10px; border: 1px dashed #020C87; background: #FFF; padding: 15px; margin-block: 2em; width: 100%;}

/*Timezone*/
.start-time .flatpickr-input, .end-time .flatpickr-input, .start-time1 .flatpickr-input, .end-time1 .flatpickr-input { border-radius: 10px; border: 1px solid #DCDDE8; background: #FFF; padding: 19px !important; width: 120px !important; }
.start-time { display: none ; }

/*Second stepper form*/
.login-side-image img{width: 100%; height: 100vh;object-fit: cover;}
.blue-btn, .resend-otp-btn {border-radius: 10px; background: #020C87; padding: 18px 50px; text-align: center; color: #FFF; border: 1px solid #FFF; width: 100%;}
.register-form input[type="email"]{border-radius: 10px; outline: none; border: 1px solid rgba(220, 221, 232, 0.87); background: #FFF; padding: 14px 12px 14px 16px; outline: unset;}
input:focus{border-color: unset !important; box-shadow: none !important;}

.site_logo { display: flex; flex-direction: column; align-items: center; }
.site_logo ul { display: flex ; gap: 30px; margin-top: 2em; }
.site_logo ul li{font-size: 14px; color: #020C87;}

.services-section .custom-radio input[type="radio"]:checked + .radio-box::after, .services-section .custom-radio input[type="checkbox"]:checked + .radio-box::after { content: ""; position: absolute; top: 10px; right: 10px; background-image: url(../images/Checkbox-form.svg); background-repeat: no-repeat; height: 20px; width: 20px; }
#toggle-password-otp i { padding-bottom: 6em; }

input::-webkit-outer-spin-button, input::-webkit-inner-spin-button { -webkit-appearance: none; margin: 0; }
input[type=checkbox] { accent-color: #020C87;}

.circle-crop  { border-radius: 50% !important;}
.single-explore-item { background: #fff; border: 1px solid #edeff1; border-radius: 3px; margin-bottom: 25px; -webkit-transition: .3s linear; -moz-transition: .3s linear; -ms-transition: .3s linear; -o-transition: .3s linear; transition: .3s linear; }
.single-explore-img-info { position: absolute; bottom: -20px; left: 0; width: 100%; opacity: 0; visibility: hidden; -webkit-transition: .3s linear; -moz-transition: .3s linear; -ms-transition: .3s linear; -o-transition: .3s linear; transition: .3s linear; }
.single-explore-item:hover .single-explore-img-info{ opacity:1; visibility:visible; bottom:0px }
.single-explore-img-info button{ position: absolute; bottom: 15px; left: 15px; height: 21px; line-height: 21px; background: #ff545a; border-radius: 3px; color: #fcfcfc; text-transform: capitalize; text-align: center; font-size: 12px; }

.HolidayModal.modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; justify-content: center; align-items: center; z-index: 9999; }
.HolidayModal .modal-content { background: #fff; padding: 20px; border-radius: 10px; width: 300px; }
.HolidayModal .modal-content input { width: 100%; margin: 10px 0; padding: 8px; }
.HolidayModal .modal-content .close { float: right; font-size: 18px; cursor: pointer; }

button.add-custom-holiday-btn { border-radius: 8px; border: 1px solid #F0F0F0; padding: 8px 14px; color: #020C87; font-size: 16px; font-weight: 600; background: #FFF; margin-top: 1em; }

.exception-textarea { display: none;}
.exception-checkbox:has(input:checked) .exception-textarea { display: block;}

.services-section.custom-radio-group input[type="radio"]:checked + .radio-box { border: 2px solid #020C87; border-radius: 6px; border-radius: 10px; background: #FFF; padding: 25px; } 
.services-section.custom-radio-group span.radio-box { border-radius: 10px; border: 1px solid #DCDDE8; background: #FFF; padding: 25px; }

img.service-icon{width: 50px; height: 50px; border-radius: 5px;}

/* Checbox */
.custom-checkbox { display: inline-block; cursor: pointer; } .custom-checkbox input { display: none; } 
.checkbox-box { display: flex; flex-direction: column; align-items: flex-start; border: 1px solid #ddd; border-radius: 10px; padding: 20px; width: 180px; height: 140px; position: relative; transition: all 0.3s ease; background-color: white; } 
.label-text { font-size: 16px; font-weight: 500; color: #000; text-align: center; }

/* .custom-checkbox  input[type="checkbox"]:checked + .checkbox-box{ border: px solid #020C87;} */
input[type="checkbox"]:checked + .checkbox-box { border: 2px solid #020C87;}

.login-page:has(.is-invalid) #toggle-password i { padding-bottom: 4.3em; padding-right: 1.2em;}

.time-picker-calendar2{display: flex; justify-content: space-between; align-items: center;}
.time-picker-calendar2 .checked-time { display: none;}
.time-picker-calendar2:has(input:checked) .closed-time { display: none;}
.time-picker-calendar2:has(input:checked) .checked-time, .time-picker-calendar2 .closed-time {  display: flex; align-items: center; justify-content: end; gap: 10px;}

.stepper-navigation { position: fixed; width: 70%; padding-top: 2em; z-index: 999; height: 100px; background: #FCFCFC; }

.professional-acc-form .image-input [data-kt-image-input-action=remove] { position: absolute; top: 8em; left: 8em; }
.professional-acc-form .image-input i { font-size: 18px; color: mediumvioletred; }

.first-stepper-form .step-1 input[type="radio"].card-radio { display: none; }
.first-stepper-form .step-1  input[type="radio"].card-radio { display: none; }
.first-stepper-form .step-1 input[type="radio"].card-radio:checked + .gray-card { border: 2px solid #020C87; }

.image-wrapper.error { border: 2px solid red; border-radius: 50%; padding: 5px; }

.parent_services .service_category .radio-box { cursor: pointer; }
.parent_services .service_category.active .radio-box { border: 2px solid #020C87; }
.parent_services .sub_categories .sub_category { display: none; }
.parent_services .sub_categories .sub_category.show { margin: 20px; }
.parent_services .sub_categories:has(.sub_category.show) { padding-bottom: 30px; }

.professional-acc-form label.error {  color: orangered;}
.customer-registration:has(.error) .iti__flag-container { top: -24px;}

.my-swal-container {    z-index: 10000;}
.my-swal-popup {  border-radius: 16px !important;   box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;}
.my-swal-title { font-weight: 600 !important; color: #374151 !important;}
.my-swal-confirm-btn {  border-radius: 8px !important; font-weight: 500 !important;  padding: 12px 24px !important; font-size: 16px !important;  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;  transition: all 0.2s ease !important;}

.my-swal-confirm-btn:hover {  transform: translateY(-1px) !important;  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15) !important;}
div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm { background-color: #020C87;}

/* Input wrapper frst step: */
.pro-account-image-holder  .image-input .image-input-wrapper { width: 131px; height: 130px; border-radius: 50% !important; transform: translate(2px, 2px); }
.image-input:not(.image-input-empty) {  background-image: none !important; width: 135px;  height: 130px;}
.pro-stepper-header {  background: #fcfcfc;   margin-bottom: 6em;}
i.pass-icon {  position: absolute;  top: 44px;  right: 26px;}

.customer-registration #acc-form input[type="text"]{    margin-bottom: 0; }

/* customer-registration */

input:focus { border: 1px solid #dcdde8 !important;  outline: unset;}

.after-crop-img img,  .dz-img {    max-width: 100%;  height: auto; display: block;  }
#cropmodal .modal-dialog { max-width: 1000px; }
#cropmodal .modal-body {  max-height: calc(100vh - 220px); overflow: auto;}
#cropmodal .img-container { max-height: calc(100vh - 260px); overflow: auto;}
#cropmodal .img-container img {  max-width: 100%;   height: auto; display: block;}

.iti:has(#professional-phone-error) .iti__flag-container {  top: -26px; }

.custom_loc {  margin-bottom: 2em;}

@media(min-width:1500px){
  .services { height: auto; overflow-y: unset; padding-right: unset; }
}